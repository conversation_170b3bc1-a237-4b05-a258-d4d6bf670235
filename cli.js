#!/usr/bin/env node
/**
 * MyQ Command Line Interface
 * This CLI allows you to control your MyQ garage doors from the command line
 */

import myQClient from './client.js';
import dotenv from 'dotenv';
import readline from 'readline';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Initialize dotenv
dotenv.config();

// ES modules fix for __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Process command line arguments
const args = process.argv.slice(2);
const command = args[0]?.toLowerCase();
const serialNumber = args[1];

// Create readline interface for prompting
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Display help information
function showHelp() {
  console.log(`
MyQ CLI - Control your MyQ garage doors from the command line

Usage:
  node cli.js <command> [serialNumber]

Commands:
  list                   List all devices
  status <serialNumber>  Get the status of a specific door
  open <serialNumber>    Open a specific door
  close <serialNumber>   Close a specific door
  login                  Login with MyQ credentials
  help                   Show this help information

Examples:
  node cli.js list
  node cli.js status ABC123
  node cli.js open ABC123
  node cli.js close ABC123
  node cli.js login

Before using this CLI:
  1. Make sure you've set your MyQ credentials in the .env file
     OR use the login command to authenticate
  2. Replace "ABC123" with your actual door serial number
`);
}

// Handle errors
function handleError(error) {
  console.error(`Error: ${error.message}`);
  process.exit(1);
}

// Format device information
function formatDevice(device) {
  return {
    name: device.name,
    type: device.device_type,
    serial: device.serial_number,
    state: myQClient.getStateDescription(device.state)
  };
}

// Prompt user for credentials
async function promptCredentials() {
  return new Promise((resolve) => {
    rl.question('Enter your MyQ email: ', (email) => {
      rl.question('Enter your MyQ password: ', (password) => {
        resolve({ email, password });
      });
    });
  });
}

// Login with credentials
async function handleLogin() {
  try {
    // First check if credentials are in .env file
    if (process.env.MYQ_EMAIL && process.env.MYQ_PASSWORD) {
      console.log('Using credentials from .env file');
      myQClient.email = process.env.MYQ_EMAIL;
      myQClient.password = process.env.MYQ_PASSWORD;
    } else {
      // Prompt for credentials
      const credentials = await promptCredentials();
      myQClient.email = credentials.email;
      myQClient.password = credentials.password;
      
      // Optionally save to .env file
      rl.question('Save credentials to .env file? (y/n): ', async (answer) => {
        if (answer.toLowerCase() === 'y') {
          try {
            const envContent = `
# MyQ Credentials
MYQ_EMAIL=${credentials.email}
MYQ_PASSWORD=${credentials.password}

# Server Configuration
PORT=3000
`;
            await fs.writeFile(path.join(__dirname, '.env'), envContent);
            console.log('Credentials saved to .env file');
          } catch (err) {
            console.error('Failed to save credentials:', err.message);
          }
        }
        rl.close();
      });
    }
    
    // Test login
    await myQClient.login();
    console.log('Successfully logged in to MyQ');
    return true;
  } catch (error) {
    console.error('Login failed:', error.message);
    return false;
  }
}

// Main function to handle commands
async function main() {
  try {
    // If no command provided or help command, show help
    if (!command || command === 'help') {
      showHelp();
      rl.close();
      return;
    }

    // Handle login command
    if (command === 'login') {
      const success = await handleLogin();
      if (!success) {
        process.exit(1);
      }
      return;
    }

    // For other commands, login first
    await myQClient.login();

    // Process the command
    switch(command) {
      case 'list':
        const devices = await myQClient.getDevices();
        console.log('\nMyQ Devices:');
        devices.forEach(device => {
          const formattedDevice = formatDevice(device);
          console.log(`\n- Name: ${formattedDevice.name}`);
          console.log(`  Type: ${formattedDevice.type}`);
          console.log(`  Serial: ${formattedDevice.serial}`);
          console.log(`  State: ${formattedDevice.state}`);
        });
        break;
        
      case 'status':
        if (!serialNumber) {
          console.error('Error: Serial number is required');
          showHelp();
          process.exit(1);
        }
        
        const state = await myQClient.getDoorState(serialNumber);
        console.log(`\nDoor Status (${serialNumber}):`);
        console.log(`State: ${state.stateDescription}`);
        break;
        
      case 'open':
        if (!serialNumber) {
          console.error('Error: Serial number is required');
          showHelp();
          process.exit(1);
        }
        
        console.log(`\nOpening door ${serialNumber}...`);
        await myQClient.openDoor(serialNumber);
        console.log('Command sent successfully');
        break;
        
      case 'close':
        if (!serialNumber) {
          console.error('Error: Serial number is required');
          showHelp();
          process.exit(1);
        }
        
        console.log(`\nClosing door ${serialNumber}...`);
        await myQClient.closeDoor(serialNumber);
        console.log('Command sent successfully');
        break;
        
      default:
        console.error(`Error: Unknown command '${command}'`);
        showHelp();
        process.exit(1);
    }
    rl.close();
  } catch (error) {
    handleError(error);
  }
}

// Execute main function
main();
