.dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
}

.dashboard-header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h1 {
  margin: 0;
  color: #333;
  font-size: 28px;
  font-weight: 700;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.refresh-button,
.logout-button,
.retry-button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-button {
  background: #3498db;
  color: white;
}

.refresh-button:hover:not(:disabled) {
  background: #2980b9;
  transform: translateY(-1px);
}

.refresh-button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
}

.logout-button {
  background: #e74c3c;
  color: white;
}

.logout-button:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

.retry-button {
  background: #27ae60;
  color: white;
  margin-top: 16px;
}

.retry-button:hover {
  background: #229954;
  transform: translateY(-1px);
}

.dashboard-main {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  width: 100%;
}

.error-banner {
  background: #fee;
  color: #c33;
  padding: 16px 20px;
  border-radius: 8px;
  border: 1px solid #fcc;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-close {
  background: none;
  border: none;
  color: #c33;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-state {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e1e5e9;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  color: #666;
  font-size: 16px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-state h2 {
  color: #333;
  margin-bottom: 12px;
}

.empty-state p {
  color: #666;
  font-size: 16px;
  margin-bottom: 24px;
}

.devices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.dashboard-footer {
  background: white;
  border-top: 1px solid #e1e5e9;
  padding: 20px 0;
  text-align: center;
}

.dashboard-footer p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .header-content h1 {
    font-size: 24px;
  }
  
  .devices-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-main {
    padding: 20px;
  }
}
