.garage-door-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.garage-door-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.garage-door-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.garage-door-header h3 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.status-indicator {
  padding: 6px 12px;
  border-radius: 20px;
  color: white;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.garage-door-info {
  margin-bottom: 20px;
}

.garage-door-info p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}

.garage-door-visual {
  display: flex;
  justify-content: center;
  margin: 24px 0;
}

.door-icon {
  width: 120px;
  height: 80px;
  position: relative;
  border: 3px solid #333;
  border-radius: 4px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.door-icon.open {
  transform: perspective(200px) rotateX(-45deg);
  background: #e8f5e8;
}

.door-icon.closed {
  background: #fff5f5;
}

.door-icon.opening,
.door-icon.closing {
  animation: doorMoving 1s infinite alternate;
}

@keyframes doorMoving {
  0% { transform: perspective(200px) rotateX(0deg); }
  100% { transform: perspective(200px) rotateX(-22deg); }
}

.door-frame {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 4px;
}

.door-panel {
  flex: 1;
  background: linear-gradient(90deg, #ddd 0%, #eee 50%, #ddd 100%);
  border-radius: 2px;
  border: 1px solid #ccc;
}

.control-button {
  width: 100%;
  padding: 14px 20px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.control-button.closed {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
}

.control-button.open {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.control-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.control-button:disabled {
  background: #bdc3c7;
  color: #7f8c8d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.control-button.opening,
.control-button.closing {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}
