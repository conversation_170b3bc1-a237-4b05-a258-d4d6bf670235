export interface MyQDevice {
  serial: string;
  name: string;
  type: string;
  state: string;
  online: boolean;
  lastUpdate: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
}

export interface DevicesResponse {
  devices: MyQDevice[];
}

export interface ExecuteCommandRequest {
  command: string;
}

export interface ExecuteCommandResponse {
  success: boolean;
  message: string;
}

export interface ApiError {
  error: string;
}
