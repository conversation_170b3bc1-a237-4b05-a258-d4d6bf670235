{"name": "@adobe/fetch", "version": "4.1.0", "description": "Light-weight Fetch implementation transparently supporting both HTTP/1(.1) and HTTP/2", "main": "./src/index.js", "module": "./src/index.js", "sideEffects": false, "type": "module", "scripts": {"lint": "eslint .", "test": "c8 mocha", "test-ci": "c8 mocha", "semantic-release": "semantic-release", "prepare": "husky install"}, "mocha": {"timeout": "5000", "recursive": "true", "reporter": "mocha-multi-reporters", "reporter-options": "configFile=.mocha-multi.json"}, "engines": {"node": ">=14.16"}, "types": "src/index.d.ts", "repository": {"type": "git", "url": "https://github.com/adobe/fetch"}, "author": "", "license": "Apache-2.0", "bugs": {"url": "https://github.com/adobe/fetch/issues"}, "homepage": "https://github.com/adobe/fetch#readme", "keywords": ["fetch", "whatwg", "Fetch API", "http", "https", "http2", "h2", "promise", "async", "request", "RFC 7234", "7234", "caching", "cache"], "dependencies": {"debug": "4.3.4", "http-cache-semantics": "4.1.1", "lru-cache": "7.18.3"}, "devDependencies": {"@semantic-release/changelog": "6.0.3", "@semantic-release/git": "10.0.1", "c8": "8.0.1", "chai": "4.3.8", "chai-as-promised": "7.1.1", "chai-bytes": "0.1.2", "chai-iterator": "3.0.2", "eslint": "8.49.0", "eslint-config-airbnb-base": "15.0.0", "eslint-plugin-header": "3.1.1", "eslint-plugin-import": "2.28.1", "formdata-node": "5.0.1", "husky": "8.0.3", "lint-staged": "14.0.1", "mocha": "10.2.0", "mocha-multi-reporters": "1.5.1", "nock": "13.3.3", "parse-cache-control": "1.0.1", "parse-multipart-data": "1.5.0", "semantic-release": "21.1.1", "sinon": "15.2.0", "stream-buffers": "3.0.2"}, "lint-staged": {"*.js": "eslint"}}