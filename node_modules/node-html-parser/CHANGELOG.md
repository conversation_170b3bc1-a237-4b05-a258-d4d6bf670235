# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [6.1.10](https://github.com/taoqf/node-fast-html-parser/compare/v6.1.9...v6.1.10) (2023-09-15)


### Bug Fixes

* [#242](https://github.com/taoqf/node-fast-html-parser/issues/242) ([36670f4](https://github.com/taoqf/node-fast-html-parser/commit/36670f4bb15f18b3cdfec0484f67c4cb891f1e79))
* [#251](https://github.com/taoqf/node-fast-html-parser/issues/251) ([42365de](https://github.com/taoqf/node-fast-html-parser/commit/42365dee6b28da5ab4017508b1ba15be503f001e))

### [6.1.9](https://github.com/taoqf/node-fast-html-parser/compare/v6.1.8...v6.1.9) (2023-09-11)


### Bug Fixes

* [#249](https://github.com/taoqf/node-fast-html-parser/issues/249) ([5a0a795](https://github.com/taoqf/node-fast-html-parser/commit/5a0a79573d51da9eccf6d13647413447eba413b1))
* test code for issue 249 ([799ee31](https://github.com/taoqf/node-fast-html-parser/commit/799ee31155a0635955b0a85e3d21ec4967223a25))

### [6.1.8](https://github.com/taoqf/node-fast-html-parser/compare/v6.1.7...v6.1.8) (2023-09-08)


### Bug Fixes

* [#248](https://github.com/taoqf/node-fast-html-parser/issues/248) void tag ([01ff9f4](https://github.com/taoqf/node-fast-html-parser/commit/01ff9f41e416d0ef0c47bc40c282d6aeb405e100))

### [6.1.7](https://github.com/taoqf/node-fast-html-parser/compare/v6.1.6...v6.1.7) (2023-09-08)


### Bug Fixes

* block text elements [#242](https://github.com/taoqf/node-fast-html-parser/issues/242) ([97e593c](https://github.com/taoqf/node-fast-html-parser/commit/97e593c2bdefbfefd35c2c9ccaef914517d60fe7))
* docs [#245](https://github.com/taoqf/node-fast-html-parser/issues/245) ([f656c7b](https://github.com/taoqf/node-fast-html-parser/commit/f656c7b2de2a62a9a363f797c6f03a2477e7312e))
* remove test code ([d3980c5](https://github.com/taoqf/node-fast-html-parser/commit/d3980c5fb7744d6fda4b270619bb581a19a4cb18))

### [6.1.6](https://github.com/taoqf/node-fast-html-parser/compare/v6.1.5...v6.1.6) (2023-08-17)


### Bug Fixes

* [#203](https://github.com/taoqf/node-fast-html-parser/issues/203) ([6006c52](https://github.com/taoqf/node-fast-html-parser/commit/6006c52c20e6c3c07405c808c8d81fa78425e794))
* [#218](https://github.com/taoqf/node-fast-html-parser/issues/218) ([6f6c824](https://github.com/taoqf/node-fast-html-parser/commit/6f6c824df54fa53af47956048b42ea47cd3b53bf))
* add tests for issue [#242](https://github.com/taoqf/node-fast-html-parser/issues/242) ([7615e27](https://github.com/taoqf/node-fast-html-parser/commit/7615e27e3eea47142beff11101d293c4f93cb6ce))
* do not decode special chractors [#240](https://github.com/taoqf/node-fast-html-parser/issues/240) ([cdadb13](https://github.com/taoqf/node-fast-html-parser/commit/cdadb132f681ca587d17df991d09ff8d22997f4e))
* improve constructor types [#230](https://github.com/taoqf/node-fast-html-parser/issues/230) ([24fd055](https://github.com/taoqf/node-fast-html-parser/commit/24fd055913125a964cb5aa61330376274c938035))
* more change about types [#230](https://github.com/taoqf/node-fast-html-parser/issues/230) ([2852d20](https://github.com/taoqf/node-fast-html-parser/commit/2852d20f865dc18a8dc6727d5b5e586282e7d50d))
* remove test code ([f0b176e](https://github.com/taoqf/node-fast-html-parser/commit/f0b176eb1ba5b5bc162744a37adcd18d3d64a515))

### [6.1.5](https://github.com/taoqf/node-fast-html-parser/compare/v6.1.4...v6.1.5) (2023-02-21)

### [6.1.4](https://github.com/taoqf/node-fast-html-parser/compare/v6.1.3...v6.1.4) (2022-11-15)


### Bug Fixes

* [#224](https://github.com/taoqf/node-fast-html-parser/issues/224) ([0719bf0](https://github.com/taoqf/node-fast-html-parser/commit/0719bf031c3fec51611f9dff922f46bfe4acb060))

### [6.1.3](https://github.com/taoqf/node-fast-html-parser/compare/v6.1.2...v6.1.3) (2022-11-14)

### [6.1.2](https://github.com/taoqf/node-fast-html-parser/compare/v6.1.1...v6.1.2) (2022-11-14)


### Bug Fixes

* [#224](https://github.com/taoqf/node-fast-html-parser/issues/224) ([fc367fa](https://github.com/taoqf/node-fast-html-parser/commit/fc367fa294d72794a0dea49edbd986d527a6314b))

### [6.1.1](https://github.com/taoqf/node-fast-html-parser/compare/v6.1.0...v6.1.1) (2022-09-24)


### Bug Fixes

* parse comments ([82b68ff](https://github.com/taoqf/node-fast-html-parser/commit/82b68ff9eb944e0c55ca2e0ea13fb714e2004803))

## [6.1.0](https://github.com/taoqf/node-fast-html-parser/compare/v6.0.0...v6.1.0) (2022-09-19)


### Features

* Add docs ([8a38eed](https://github.com/taoqf/node-fast-html-parser/commit/8a38eedab6b20906ee89dea86c4271960afbad2d))

## [6.0.0](https://github.com/taoqf/node-fast-html-parser/compare/v5.4.2-0...v6.0.0) (2022-09-08)


### Bug Fixes

* Preserve invalid nested A tags in AST (see [#215](https://github.com/taoqf/node-fast-html-parser/issues/215) for detail) ([374188f](https://github.com/taoqf/node-fast-html-parser/commit/374188f1c6d6c6d0567348b8e8d20957f5a93fb8))

### [5.4.2](https://github.com/taoqf/node-fast-html-parser/compare/v5.4.2-0...v5.4.2) (2022-08-30)

## [5.1.0](https://github.com/taoqf/node-fast-html-parser/compare/v4.1.5...v5.1.0) (2021-10-28)

### Features

* Exposed `HTMLElement#rawAttrs` (made public) ([34f1595](https://github.com/taoqf/node-fast-html-parser/commit/34f1595756c0974b6ae7ef5755a615f09e421f32))

## [5.0.0](https://github.com/taoqf/node-fast-html-parser/compare/v4.1.5...v5.0.0) (2021-10-10)


### ⚠ BREAKING CHANGES

* Added esm named export support ([0d4b922](https://github.com/taoqf/node-fast-html-parser/commit/0d4b922eefd6210fe802991e464b21b0c69d5f63))

### Features

* Added esm named export support (closes [#160](https://github.com/taoqf/node-fast-html-parser/issues/160) closes [#139](https://github.com/taoqf/node-fast-html-parser/issues/139)) ([0d4b922](https://github.com/taoqf/node-fast-html-parser/commit/0d4b922eefd6210fe802991e464b21b0c69d5f63))
* Added HTMLElement#getElementsByTagName ([d462e44](https://github.com/taoqf/node-fast-html-parser/commit/d462e449e7ebb00a5a43fb574133681ad5a62475))
* Improved parsing performance + matching (closes [#164](https://github.com/taoqf/node-fast-html-parser/issues/164)) ([3c5b8e2](https://github.com/taoqf/node-fast-html-parser/commit/3c5b8e2a9104b01a8ca899a7970507463e42adaf))


### Bug Fixes

* Add null to return type for HTMLElement#querySelector (closes [#157](https://github.com/taoqf/node-fast-html-parser/issues/157)) ([2b65583](https://github.com/taoqf/node-fast-html-parser/commit/2b655839bd3868c41fb19cae5786ca097565bc7f))
* blockTextElements incorrectly matching partial tag (detail) (fixes [#156](https://github.com/taoqf/node-fast-html-parser/issues/156) fixes [#124](https://github.com/taoqf/node-fast-html-parser/issues/124)) ([6823349](https://github.com/taoqf/node-fast-html-parser/commit/6823349fdf1809c7484c70d948aa24930ef4983f))
