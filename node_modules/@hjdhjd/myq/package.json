{"name": "@hjdhjd/myq", "type": "module", "version": "7.6.0", "displayName": "myQ API", "description": "A nearly complete and modern implementation of the Liftmaster / Chamberlain myQ API.", "author": {"name": "HJD", "url": "https://github.com/hjdhjd"}, "homepage": "https://github.com/hjdhjd/myq#readme", "license": "ISC", "repository": {"type": "git", "url": "git://github.com/hjdhjd/myq.git"}, "bugs": {"url": "https://github.com/hjdhjd/myq/issues"}, "keywords": ["chamberlain", "craftsman", "door", "garage", "garage door", "liftmaster", "myq", "remote"], "engines": {"node": ">=18"}, "main": "dist/index.js", "scripts": {"build": "rimraf ./dist && tsc", "clean": "rimraf ./dist", "lint": "eslint src/**.ts", "postpublish": "npm run clean", "prepublishOnly": "npm run lint && npm run build", "test": "eslint src/**.ts", "watch": "npm run build && npm link && nodemon"}, "devDependencies": {"@types/node": "20.8.6", "@typescript-eslint/eslint-plugin": "6.7.5", "@typescript-eslint/parser": "6.7.5", "eslint": "8.51.0", "rimraf": "5.0.5", "typescript": "5.2.2"}, "dependencies": {"@adobe/fetch": "4.1.0", "node-html-parser": "6.1.10", "pkce-challenge": "4.0.1"}}