/* Copyright(C) 2017-2023, HJ<PERSON> (https://github.com/hjdhjd). All rights reserved.
 *
 * myq-types.ts: Type definitions for myQ.
 */
export {};
/*
 * // List all the door types we know about. For future use...
 * const myQDoorTypes = [
 *   "commercialdooropener",
 *   "garagedooropener",
 *   "gate",
 *   "virtualgaragedooropener",
 *   "wifigaragedooropener"
 *  ];
 */
//# sourceMappingURL=myq-types.js.map