{"name": "pkce-challenge", "version": "4.0.1", "description": "Generate or verify a Proof Key for Code Exchange (PKCE) challenge pair", "source": "src/index.ts", "main": "dist/index.js", "type": "module", "types": "dist/index.d.ts", "files": ["dist/"], "scripts": {"watch": "tsc --watch --declaration", "build": "tsc --declaration", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js"}, "repository": {"type": "git", "url": "git+https://github.com/crouchcd/pkce-challenge.git"}, "keywords": ["PKCE", "oauth2"], "author": "crouchcd", "license": "MIT", "bugs": {"url": "https://github.com/crouchcd/pkce-challenge/issues"}, "homepage": "https://github.com/crouchcd/pkce-challenge#readme", "engines": {"node": ">=16.20.0"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^18.15.11", "jest": "^29.5.0", "typescript": "^5.0.3"}}