import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { myQApi } from '@hjdhjd/myq';
// Load environment variables
dotenv.config();
const app = express();
const PORT = process.env.PORT || 3001;
// Middleware
app.use(cors());
app.use(express.json());
// Global MyQ API instance
let myqApiInstance = null;
let isLoggedIn = false;
// Custom logging for MyQ
const myqLogger = {
    info: (message) => console.log(`[MyQ INFO] ${message}`),
    warn: (message) => console.warn(`[MyQ WARN] ${message}`),
    error: (message) => console.error(`[MyQ ERROR] ${message}`),
    debug: (message) => console.debug(`[MyQ DEBUG] ${message}`)
};
// Initialize MyQ API
function initializeMyQ() {
    myqApiInstance = new myQApi(myqLogger);
    console.log('MyQ API initialized');
}
// Routes
// Health check
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        myqConnected: isLoggedIn
    });
});
// Login to MyQ
app.post('/api/login', async (req, res) => {
    try {
        const { email, password } = req.body;
        if (!email || !password) {
            return res.status(400).json({
                error: 'Email and password are required'
            });
        }
        if (!myqApiInstance) {
            initializeMyQ();
        }
        console.log('Attempting to login to MyQ...');
        console.log(`Login attempt with email: ${email}`);
        const loginSuccess = await myqApiInstance.login(email, password);
        if (loginSuccess) {
            isLoggedIn = true;
            console.log('Successfully logged in to MyQ');
            res.json({
                success: true,
                message: 'Successfully logged in to MyQ'
            });
        }
        else {
            isLoggedIn = false;
            res.status(401).json({
                error: 'Failed to login to MyQ. Please check your credentials.'
            });
        }
    }
    catch (error) {
        console.error('Login error:', error);
        isLoggedIn = false;
        res.status(500).json({
            error: 'Internal server error during login'
        });
    }
});
// Get all devices
app.get('/api/devices', async (req, res) => {
    try {
        if (!isLoggedIn) {
            return res.status(401).json({
                error: 'Not logged in to MyQ'
            });
        }
        console.log('Refreshing devices...');
        const refreshSuccess = await myqApiInstance.refreshDevices();
        if (!refreshSuccess) {
            return res.status(500).json({
                error: 'Failed to refresh devices'
            });
        }
        const devices = myqApiInstance.devices.map((device) => ({
            serial: device.serial_number,
            name: myqApiInstance.getDeviceName(device),
            type: device.device_family,
            state: device.state?.door_state || device.state?.lamp_state || 'unknown',
            online: device.state?.online || false,
            lastUpdate: device.state?.last_update || null
        }));
        res.json({ devices });
    }
    catch (error) {
        console.error('Error getting devices:', error);
        res.status(500).json({
            error: 'Failed to get devices'
        });
    }
});
// Get specific device
app.get('/api/devices/:serial', async (req, res) => {
    try {
        if (!myqApiInstance || !isLoggedIn) {
            return res.status(401).json({
                error: 'Not logged in to MyQ'
            });
        }
        const { serial } = req.params;
        const device = myqApiInstance.getDevice(serial);
        if (!device) {
            return res.status(404).json({
                error: 'Device not found'
            });
        }
        res.json({
            serial: device.serial_number,
            name: myqApiInstance.getDeviceName(device),
            type: device.device_family,
            state: device.state?.door_state || device.state?.lamp_state || 'unknown',
            online: device.state?.online || false,
            lastUpdate: device.state?.last_update || null,
            hwInfo: myqApiInstance.getHwInfo(serial)
        });
    }
    catch (error) {
        console.error('Error getting device:', error);
        res.status(500).json({
            error: 'Failed to get device'
        });
    }
});
// Execute command on device
app.post('/api/devices/:serial/execute', async (req, res) => {
    try {
        if (!myqApiInstance || !isLoggedIn) {
            return res.status(401).json({
                error: 'Not logged in to MyQ'
            });
        }
        const { serial } = req.params;
        const { command } = req.body;
        if (!command) {
            return res.status(400).json({
                error: 'Command is required'
            });
        }
        const device = myqApiInstance.getDevice(serial);
        if (!device) {
            return res.status(404).json({
                error: 'Device not found'
            });
        }
        console.log(`Executing command "${command}" on device ${serial}`);
        const success = await myqApiInstance.execute(device, command);
        if (success) {
            res.json({
                success: true,
                message: `Command "${command}" executed successfully`
            });
        }
        else {
            res.status(500).json({
                error: `Failed to execute command "${command}"`
            });
        }
    }
    catch (error) {
        console.error('Error executing command:', error);
        res.status(500).json({
            error: 'Failed to execute command'
        });
    }
});
// Logout
app.post('/api/logout', (req, res) => {
    isLoggedIn = false;
    myqApiInstance = null;
    res.json({
        success: true,
        message: 'Logged out successfully'
    });
});
// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        error: 'Internal server error'
    });
});
// Start server
app.listen(PORT, () => {
    console.log(`MyQ Backend API server running on port ${PORT}`);
    console.log(`Health check: http://localhost:${PORT}/api/health`);
});
//# sourceMappingURL=index.js.map