import React, { createContext, useContext, useState, useCallback } from 'react';
import type { MyQDevice, LoginRequest } from '../types/myq';
import { myqApi } from '../services/api';

interface MyQContextType {
  isLoggedIn: boolean;
  devices: MyQDevice[];
  loading: boolean;
  error: string | null;
  login: (credentials: LoginRequest) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshDevices: () => Promise<void>;
  executeCommand: (serial: string, command: string) => Promise<boolean>;
  clearError: () => void;
}

const MyQContext = createContext<MyQContextType | undefined>(undefined);

export const useMyQ = () => {
  const context = useContext(MyQContext);
  if (context === undefined) {
    throw new Error('useMyQ must be used within a MyQProvider');
  }
  return context;
};

interface MyQProviderProps {
  children: React.ReactNode;
}

export const MyQProvider: React.FC<MyQProviderProps> = ({ children }) => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [devices, setDevices] = useState<MyQDevice[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const refreshDevices = useCallback(async () => {
    if (!isLoggedIn) return;

    setLoading(true);
    setError(null);

    try {
      const response = await myqApi.getDevices();
      setDevices(response.devices);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to refresh devices');
    } finally {
      setLoading(false);
    }
  }, [isLoggedIn]);

  const login = useCallback(async (credentials: LoginRequest): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await myqApi.login(credentials);
      if (response.success) {
        setIsLoggedIn(true);
        // Don't call refreshDevices here to avoid circular dependency
        return true;
      } else {
        setError('Login failed');
        return false;
      }
    } catch (err: any) {
      setError(err.response?.data?.error || 'Login failed');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const logout = useCallback(async () => {
    setLoading(true);
    try {
      await myqApi.logout();
      setIsLoggedIn(false);
      setDevices([]);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Logout failed');
    } finally {
      setLoading(false);
    }
  }, []);



  const executeCommand = useCallback(async (serial: string, command: string): Promise<boolean> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await myqApi.executeCommand(serial, { command });
      if (response.success) {
        // Refresh devices to get updated state
        await refreshDevices();
        return true;
      } else {
        setError('Command execution failed');
        return false;
      }
    } catch (err: any) {
      setError(err.response?.data?.error || 'Command execution failed');
      return false;
    } finally {
      setLoading(false);
    }
  }, [refreshDevices]);

  const value: MyQContextType = {
    isLoggedIn,
    devices,
    loading,
    error,
    login,
    logout,
    refreshDevices,
    executeCommand,
    clearError,
  };

  return <MyQContext.Provider value={value}>{children}</MyQContext.Provider>;
};
