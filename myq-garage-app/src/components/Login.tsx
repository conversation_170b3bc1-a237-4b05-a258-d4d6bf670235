import React, { useState } from 'react';
import { useMyQ } from '../context/MyQContext';
import './Login.css';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const { login, loading, error, clearError } = useMyQ();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    
    if (!email || !password) {
      return;
    }

    await login({ email, password });
  };

  return (
    <div className="login-container">
      <div className="login-card">
        <h1>MyQ Garage Door Control</h1>
        <p>Sign in to your MyQ account to control your garage doors</p>
        
        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your MyQ email"
              required
              disabled={loading}
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your MyQ password"
              required
              disabled={loading}
            />
          </div>
          
          {error && (
            <div className="error-message">
              {error}
            </div>
          )}
          
          <button 
            type="submit" 
            className="login-button"
            disabled={loading || !email || !password}
          >
            {loading ? 'Signing in...' : 'Sign In'}
          </button>
        </form>
        
        <div className="login-info">
          <p><strong>Note:</strong> This app uses the hjdhjd/myq library to connect to your MyQ account.</p>
          <p>Your credentials are only used to authenticate with the MyQ service.</p>
        </div>
      </div>
    </div>
  );
};

export default Login;
