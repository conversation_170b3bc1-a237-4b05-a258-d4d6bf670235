import React, { useEffect } from 'react';
import { useMyQ } from '../context/MyQContext';
import GarageDoor from './GarageDoor';
import './Dashboard.css';

const Dashboard: React.FC = () => {
  const { devices, loading, error, refreshDevices, logout, clearError } = useMyQ();

  useEffect(() => {
    refreshDevices();
  }, [refreshDevices]);

  const handleRefresh = () => {
    clearError();
    refreshDevices();
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <div className="header-content">
          <h1>MyQ Garage Door Control</h1>
          <div className="header-actions">
            <button 
              className="refresh-button"
              onClick={handleRefresh}
              disabled={loading}
            >
              {loading ? 'Refreshing...' : 'Refresh'}
            </button>
            <button 
              className="logout-button"
              onClick={handleLogout}
            >
              Logout
            </button>
          </div>
        </div>
      </header>

      <main className="dashboard-main">
        {error && (
          <div className="error-banner">
            <span>{error}</span>
            <button onClick={clearError} className="error-close">×</button>
          </div>
        )}

        {loading && devices.length === 0 ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>Loading your garage doors...</p>
          </div>
        ) : devices.length === 0 ? (
          <div className="empty-state">
            <h2>No Garage Doors Found</h2>
            <p>No MyQ devices were found in your account.</p>
            <button onClick={handleRefresh} className="retry-button">
              Try Again
            </button>
          </div>
        ) : (
          <div className="devices-grid">
            {devices.map((device) => (
              <GarageDoor key={device.serial} device={device} />
            ))}
          </div>
        )}
      </main>

      <footer className="dashboard-footer">
        <p>
          Powered by <strong>hjdhjd/myq</strong> library • 
          Last updated: {new Date().toLocaleTimeString()}
        </p>
      </footer>
    </div>
  );
};

export default Dashboard;
