import React from 'react';
import { useMyQ } from '../context/MyQContext';
import type { MyQDevice } from '../types/myq';
import './GarageDoor.css';

interface GarageDoorProps {
  device: MyQDevice;
}

const GarageDoor: React.FC<GarageDoorProps> = ({ device }) => {
  const { executeCommand, loading } = useMyQ();

  const handleToggle = async () => {
    const command = device.state === 'open' ? 'close' : 'open';
    await executeCommand(device.serial, command);
  };

  const getStatusColor = () => {
    if (!device.online) return '#999';
    switch (device.state) {
      case 'open': return '#e74c3c';
      case 'closed': return '#27ae60';
      case 'opening': return '#f39c12';
      case 'closing': return '#f39c12';
      default: return '#999';
    }
  };

  const getStatusText = () => {
    if (!device.online) return 'Offline';
    switch (device.state) {
      case 'open': return 'Open';
      case 'closed': return 'Closed';
      case 'opening': return 'Opening...';
      case 'closing': return 'Closing...';
      default: return 'Unknown';
    }
  };

  const getButtonText = () => {
    if (!device.online) return 'Offline';
    if (loading) return 'Processing...';
    return device.state === 'open' ? 'Close Door' : 'Open Door';
  };

  const isButtonDisabled = () => {
    return !device.online || loading || device.state === 'opening' || device.state === 'closing';
  };

  return (
    <div className="garage-door-card">
      <div className="garage-door-header">
        <h3>{device.name}</h3>
        <div 
          className="status-indicator"
          style={{ backgroundColor: getStatusColor() }}
        >
          {getStatusText()}
        </div>
      </div>
      
      <div className="garage-door-info">
        <p><strong>Serial:</strong> {device.serial}</p>
        <p><strong>Type:</strong> {device.type}</p>
        <p><strong>Last Update:</strong> {new Date(device.lastUpdate).toLocaleString()}</p>
      </div>
      
      <div className="garage-door-visual">
        <div className={`door-icon ${device.state}`}>
          <div className="door-frame">
            <div className="door-panel"></div>
            <div className="door-panel"></div>
            <div className="door-panel"></div>
            <div className="door-panel"></div>
          </div>
        </div>
      </div>
      
      <button
        className={`control-button ${device.state}`}
        onClick={handleToggle}
        disabled={isButtonDisabled()}
      >
        {getButtonText()}
      </button>
    </div>
  );
};

export default GarageDoor;
