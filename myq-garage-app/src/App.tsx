import React from 'react';
import { MyQProvider, useMyQ } from './context/MyQContext';
import Login from './components/Login';
import Dashboard from './components/Dashboard';
import './App.css';

const AppContent: React.FC = () => {
  const { isLoggedIn } = useMyQ();

  return isLoggedIn ? <Dashboard /> : <Login />;
};

function App() {
  return (
    <MyQProvider>
      <AppContent />
    </MyQProvider>
  );
}

export default App;
