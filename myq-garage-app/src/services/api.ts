import axios from 'axios';
import type { 
  LoginRequest, 
  LoginResponse, 
  DevicesResponse, 
  ExecuteCommandRequest, 
  ExecuteCommandResponse,
  MyQDevice 
} from '../types/myq';

const API_BASE_URL = 'http://localhost:3001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const myqApi = {
  // Health check
  async healthCheck() {
    const response = await api.get('/health');
    return response.data;
  },

  // Login to MyQ
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await api.post<LoginResponse>('/login', credentials);
    return response.data;
  },

  // Get all devices
  async getDevices(): Promise<DevicesResponse> {
    const response = await api.get<DevicesResponse>('/devices');
    return response.data;
  },

  // Get specific device
  async getDevice(serial: string): Promise<MyQDevice> {
    const response = await api.get<MyQDevice>(`/devices/${serial}`);
    return response.data;
  },

  // Execute command on device
  async executeCommand(serial: string, command: ExecuteCommandRequest): Promise<ExecuteCommandResponse> {
    const response = await api.post<ExecuteCommandResponse>(`/devices/${serial}/execute`, command);
    return response.data;
  },

  // Logout
  async logout() {
    const response = await api.post('/logout');
    return response.data;
  }
};

export default myqApi;
