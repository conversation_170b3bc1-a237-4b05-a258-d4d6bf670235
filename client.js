/**
 * MyQ API Client
 * This module handles communication with the MyQ API to control garage doors
 * Using @hjdhjd/myq package for more reliable API access
 */

import { myQApi } from '@hjdhjd/myq';
import dotenv from 'dotenv';

// Initialize dotenv
dotenv.config();

class MyQClient {
  constructor() {
    this.email = process.env.MYQ_EMAIL;
    this.password = process.env.MYQ_PASSWORD;
    this.api = null;
    this.authenticated = false;
    this.devices = [];
  }

  /**
   * Authenticate with the MyQ API
   */
  async login() {
    try {
      if (!this.email || !this.password) {
        throw new Error('MyQ credentials not provided in .env file');
      }
      
      this.api = new myQApi();
      await this.api.login(this.email, this.password);
      
      this.authenticated = true;
      console.log('Successfully authenticated with MyQ');
      return true;
    } catch (error) {
      console.error('Failed to authenticate with MyQ:', error.message);
      this.authenticated = false;
      throw error;
    }
  }

  /**
   * Get list of all devices
   */
  async getDevices() {
    if (!this.authenticated || !this.api) {
      await this.login();
    }

    try {
      const accounts = await this.api.getAccounts();
      
      if (!accounts || accounts.length === 0) {
        throw new Error('No MyQ accounts found');
      }
      
      // Get the devices from the first account
      await this.api.refreshDevices();
      
      // Get all devices from the API
      const devices = Object.values(this.api.devices);
      
      // Filter and transform the devices to match our expected format
      this.devices = devices.map(device => ({
        name: device.name,
        serial_number: device.serial,
        device_family: device.deviceFamily,
        device_type: device.deviceType,
        state: this.convertStateToLegacyFormat(device.state.door)
      }));
      
      return this.devices;
    } catch (error) {
      console.error('Failed to fetch devices:', error.message);
      throw error;
    }
  }
  
  /**
   * Convert new state format to legacy format used in the original client
   */
  convertStateToLegacyFormat(state) {
    const stateMapping = {
      'open': '1',
      'closed': '2',
      'stopped': '3',
      'opening': '4',
      'closing': '5',
      'unknown': '9'
    };
    
    return stateMapping[state?.toLowerCase()] || '9';
  }

  /**
   * Get a specific device by serial number
   */
  async getDevice(serialNumber) {
    const devices = await this.getDevices();
    const device = devices.find(d => d.serial_number === serialNumber);
    
    if (!device) {
      throw new Error(`Device with serial number ${serialNumber} not found`);
    }
    
    return device;
  }

  /**
   * Open a garage door
   */
  async openDoor(serialNumber) {
    if (!this.authenticated || !this.api) {
      await this.login();
    }

    try {
      await this.api.execute(serialNumber, 'door', 'open');
      return { status: 'opening', serialNumber };
    } catch (error) {
      console.error(`Failed to open door ${serialNumber}:`, error.message);
      throw error;
    }
  }

  /**
   * Close a garage door
   */
  async closeDoor(serialNumber) {
    if (!this.authenticated || !this.api) {
      await this.login();
    }

    try {
      await this.api.execute(serialNumber, 'door', 'close');
      return { status: 'closing', serialNumber };
    } catch (error) {
      console.error(`Failed to close door ${serialNumber}:`, error.message);
      throw error;
    }
  }

  /**
   * Get the current state of a door
   */
  async getDoorState(serialNumber) {
    const device = await this.getDevice(serialNumber);
    return {
      serialNumber,
      state: device.state,
      stateDescription: this.getStateDescription(device.state)
    };
  }

  /**
   * Convert state code to human-readable description
   */
  getStateDescription(state) {
    const states = {
      '1': 'open',
      '2': 'closed',
      '3': 'stopped',
      '4': 'opening',
      '5': 'closing',
      '9': 'unknown'
    };
    
    return states[state] || 'unknown';
  }
}

// Create and export a singleton instance
const myQClient = new MyQClient();
export default myQClient;
