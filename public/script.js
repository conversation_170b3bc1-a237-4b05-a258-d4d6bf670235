/**
 * MyQ Garage Door Control - Client-side JavaScript
 */

document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const apiKeyInput = document.getElementById('api-key');
    const saveKeyButton = document.getElementById('save-key');
    const emailInput = document.getElementById('myq-email');
    const passwordInput = document.getElementById('myq-password');
    const loginButton = document.getElementById('login-button');
    const refreshDevicesButton = document.getElementById('refresh-devices');
    const devicesContainer = document.getElementById('devices-container');
    const loadingElement = document.getElementById('loading');
    const errorElement = document.getElementById('error-message');
    
    // Local storage keys
    const API_KEY_STORAGE = 'myq_api_key';
    const MYQ_EMAIL_STORAGE = 'myq_email';
    const AUTH_TYPE_STORAGE = 'myq_auth_type'; // 'api_key' or 'direct_login'
    
    // API Base URL
    const API_BASE_URL = '/api';
    
    // Hide loading message initially
    loadingElement.style.display = 'none';
    
    // Initialize - Load saved auth info
    const authType = localStorage.getItem(AUTH_TYPE_STORAGE);
    const savedApiKey = localStorage.getItem(API_KEY_STORAGE);
    const savedEmail = localStorage.getItem(MYQ_EMAIL_STORAGE);
    
    if (authType === 'api_key' && savedApiKey) {
        apiKeyInput.value = savedApiKey;
        fetchDevices(); // Auto load devices if API key exists
    } else if (authType === 'direct_login' && savedEmail) {
        emailInput.value = savedEmail;
        fetchDevices(); // Auto load devices if previously logged in
    }
    
    // Event Listeners
    saveKeyButton.addEventListener('click', saveApiKey);
    loginButton.addEventListener('click', login);
    refreshDevicesButton.addEventListener('click', fetchDevices);
    
    // Save API Key to local storage
    function saveApiKey() {
        const apiKey = apiKeyInput.value.trim();
        
        if (!apiKey) {
            showError('API key cannot be empty');
            return;
        }
        
        localStorage.setItem(API_KEY_STORAGE, apiKey);
        localStorage.setItem(AUTH_TYPE_STORAGE, 'api_key');
        showError(''); // Clear any errors
        fetchDevices(); // Fetch devices with new key
    }
    
    // Login with email and password
    async function login() {
        const email = emailInput.value.trim();
        const password = passwordInput.value.trim();
        
        if (!email || !password) {
            showError('Email and password are required');
            return;
        }
        
        showLoading(true);
        showError(''); // Clear previous errors
        
        try {
            const response = await fetch(`${API_BASE_URL}/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email, password })
            });
            
            if (!response.ok) {
                const data = await response.json();
                throw new Error(data.error || `HTTP error! Status: ${response.status}`);
            }
            
            // Store email for future reference (never store password)
            localStorage.setItem(MYQ_EMAIL_STORAGE, email);
            localStorage.setItem(AUTH_TYPE_STORAGE, 'direct_login');
            
            // Clear password field for security
            passwordInput.value = '';
            
            // Fetch devices after successful login
            fetchDevices();
        } catch (error) {
            console.error('Login error:', error);
            showError(`Login failed: ${error.message}`);
            showLoading(false);
        }
    }
    
    // Fetch all devices from API
    async function fetchDevices() {
        const authType = localStorage.getItem(AUTH_TYPE_STORAGE);
        const apiKey = localStorage.getItem(API_KEY_STORAGE);
        const headers = {};
        
        if (authType === 'api_key') {
            if (!apiKey) {
                showError('Please enter and save an API key first');
                return;
            }
            headers['x-api-key'] = apiKey;
        } else if (authType === 'direct_login') {
            // Server will use the session for authentication
            // No additional headers needed
        } else {
            showError('Please login or provide an API key first');
            return;
        }
        
        showLoading(true);
        showError(''); // Clear previous errors
        
        try {
            const response = await fetch(`${API_BASE_URL}/devices`, {
                headers: headers
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            
            const data = await response.json();
            renderDevices(data.devices);
        } catch (error) {
            console.error('Error fetching devices:', error);
            showError('Failed to load devices. Please check your credentials and ensure the server is running.');
        } finally {
            showLoading(false);
        }
    }
    
    // Render devices to the DOM
    function renderDevices(devices) {
        // Clear the container first
        devicesContainer.innerHTML = '';
        
        if (!devices || devices.length === 0) {
            devicesContainer.innerHTML = '<p>No devices found</p>';
            return;
        }
        
        // Filter for garage door openers only
        const garageDoors = devices.filter(device => 
            device.device_type === 'garage_door_opener');
        
        if (garageDoors.length === 0) {
            devicesContainer.innerHTML = '<p>No garage doors found</p>';
            return;
        }
        
        // Create a card for each garage door
        garageDoors.forEach(door => {
            const deviceElement = createDeviceElement(door);
            devicesContainer.appendChild(deviceElement);
        });
    }
    
    // Create a device card element
    function createDeviceElement(device) {
        const deviceDiv = document.createElement('div');
        deviceDiv.className = 'device';
        deviceDiv.setAttribute('data-serial', device.serial_number);
        
        const doorState = getStateDescription(device.state);
        
        deviceDiv.innerHTML = `
            <h3>${device.name}</h3>
            <div class="device-info">
                <p><strong>Type:</strong> ${device.device_family}</p>
                <p><strong>Serial:</strong> ${device.serial_number}</p>
            </div>
            <span class="device-status ${doorState.toLowerCase()}">${doorState}</span>
            <div class="device-actions">
                <button class="open-btn" ${doorState.toLowerCase() === 'open' ? 'disabled' : ''}>Open Door</button>
                <button class="close-btn" ${doorState.toLowerCase() === 'closed' ? 'disabled' : ''}>Close Door</button>
            </div>
        `;
        
        // Add event listeners for buttons
        const openBtn = deviceDiv.querySelector('.open-btn');
        const closeBtn = deviceDiv.querySelector('.close-btn');
        
        openBtn.addEventListener('click', () => controlDoor(device.serial_number, 'open'));
        closeBtn.addEventListener('click', () => controlDoor(device.serial_number, 'close'));
        
        return deviceDiv;
    }
    
    // Control a door (open/close)
    async function controlDoor(serialNumber, action) {
        const authType = localStorage.getItem(AUTH_TYPE_STORAGE);
        const apiKey = localStorage.getItem(API_KEY_STORAGE);
        const headers = {};
        
        if (authType === 'api_key') {
            if (!apiKey) {
                showError('Please enter and save an API key first');
                return;
            }
            headers['x-api-key'] = apiKey;
        } else if (authType !== 'direct_login') {
            showError('Please login or provide an API key first');
            return;
        }
        
        // Find the device element and update UI
        const deviceElement = document.querySelector(`.device[data-serial="${serialNumber}"]`);
        const statusElement = deviceElement.querySelector('.device-status');
        const originalStatus = statusElement.textContent;
        
        // Update status immediately for better UX
        statusElement.textContent = action === 'open' ? 'Opening...' : 'Closing...';
        statusElement.className = `device-status ${action === 'open' ? 'opening' : 'closing'}`;
        
        // Disable both buttons during action
        const buttons = deviceElement.querySelectorAll('button');
        buttons.forEach(button => button.disabled = true);
        
        try {
            const response = await fetch(`${API_BASE_URL}/device/${serialNumber}/${action}`, {
                method: 'POST',
                headers: headers
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            
            // After successful call, refresh all devices to get current states
            setTimeout(() => {
                fetchDevices();
            }, 2000); // Wait 2 seconds before refreshing
            
        } catch (error) {
            console.error(`Error ${action}ing door:`, error);
            showError(`Failed to ${action} door. Please try again.`);
            
            // Revert status on error
            statusElement.textContent = originalStatus;
            statusElement.className = `device-status ${originalStatus.toLowerCase()}`;
            
            // Re-enable buttons
            buttons.forEach(button => {
                if ((button.classList.contains('open-btn') && originalStatus.toLowerCase() !== 'open') ||
                    (button.classList.contains('close-btn') && originalStatus.toLowerCase() !== 'closed')) {
                    button.disabled = false;
                }
            });
        }
    }
    
    // Show/hide loading indicator
    function showLoading(isLoading) {
        loadingElement.style.display = isLoading ? 'block' : 'none';
    }
    
    // Show error message
    function showError(message) {
        if (message) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        } else {
            errorElement.style.display = 'none';
        }
    }
    
    // Helper function to convert state code to readable text
    function getStateDescription(state) {
        const states = {
            '1': 'Open',
            '2': 'Closed',
            '3': 'Stopped',
            '4': 'Opening',
            '5': 'Closing',
            '9': 'Unknown'
        };
        
        return states[state] || 'Unknown';
    }
});
