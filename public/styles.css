/* My<PERSON> Garage Door Control - Styles */

:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --light-color: #ecf0f1;
    --dark-color: #34495e;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    background-color: #f8f9fa;
    color: #333;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    background-color: var(--secondary-color);
    color: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
}

h1 {
    font-size: 2.2rem;
}

h2 {
    margin-bottom: 15px;
    color: var(--secondary-color);
}

.auth-section,
.control-section {
    background: white;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.input-group {
    display: flex;
    margin-bottom: 15px;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    flex: 0 0 80px;
    padding-top: 10px;
}

input[type="password"],
input[type="text"],
input[type="email"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-size: 1rem;
}

button:hover {
    background-color: #2980b9;
}

.login-btn {
    margin-bottom: 20px;
    width: 100%;
    padding: 12px;
    font-weight: bold;
    font-size: 1.1rem;
}

.refresh-btn {
    margin-bottom: 15px;
    background-color: var(--secondary-color);
}

.refresh-btn:hover {
    background-color: #1a2530;
}

#save-key {
    margin-left: 10px;
    flex: 0 0 80px;
}

.auth-toggle {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.auth-toggle p {
    margin-bottom: 15px;
    color: #777;
    text-align: center;
    font-style: italic;
}

.devices-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.device {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: var(--shadow);
    border-top: 4px solid var(--primary-color);
}

.device h3 {
    margin-bottom: 10px;
}

.device .device-info {
    margin-bottom: 15px;
}

.device .device-info p {
    margin-bottom: 5px;
}

.device-status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: bold;
    text-transform: uppercase;
    margin-bottom: 15px;
}

.device-status.open {
    background-color: var(--success-color);
    color: white;
}

.device-status.closed {
    background-color: var(--secondary-color);
    color: white;
}

.device-status.opening,
.device-status.closing {
    background-color: var(--warning-color);
    color: white;
}

.device-actions {
    display: flex;
    justify-content: space-between;
}

.device-actions button {
    flex: 1;
    margin: 0 5px;
}

.device-actions button:first-child {
    margin-left: 0;
}

.device-actions button:last-child {
    margin-right: 0;
}

.device-actions .open-btn {
    background-color: var(--success-color);
}

.device-actions .open-btn:hover {
    background-color: #27ae60;
}

.device-actions .close-btn {
    background-color: var(--danger-color);
}

.device-actions .close-btn:hover {
    background-color: #c0392b;
}

.loading {
    text-align: center;
    font-style: italic;
    color: #777;
    margin: 20px 0;
}

.error {
    color: var(--danger-color);
    background-color: rgba(231, 76, 60, 0.1);
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
    display: none;
}

footer {
    text-align: center;
    margin-top: 30px;
    padding: 10px 0;
    color: #777;
}

@media (max-width: 600px) {
    .input-group {
        flex-direction: column;
    }
    
    .input-group label {
        margin-bottom: 5px;
    }
    
    #save-key {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
    }
    
    .device-actions {
        flex-direction: column;
    }
    
    .device-actions button {
        margin: 5px 0;
    }
}
