<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MyQ Garage Door Control</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>MyQ Garage Door Control</h1>
        </header>
        
        <div class="auth-section">
            <h2>Authentication</h2>
            <div class="input-group">
                <label for="myq-email">Email</label>
                <input type="email" id="myq-email" placeholder="Enter your MyQ email">
            </div>
            <div class="input-group">
                <label for="myq-password">Password</label>
                <input type="password" id="myq-password" placeholder="Enter your MyQ password">
            </div>
            <button id="login-button" class="login-btn">Sign In</button>
            
            <div class="auth-toggle">
                <p>Or use API key authentication:</p>
                <div class="input-group">
                    <label for="api-key">API Key</label>
                    <input type="password" id="api-key" placeholder="Enter your API Key">
                    <button id="save-key">Save</button>
                </div>
            </div>
        </div>
        
        <div class="control-section">
            <h2>Garage Doors</h2>
            <div class="loading" id="loading">Loading devices...</div>
            <div class="error" id="error-message"></div>
            <button id="refresh-devices" class="refresh-btn">Refresh Devices</button>
            
            <div id="devices-container" class="devices-container">
                <!-- Devices will be dynamically added here -->
            </div>
        </div>
        
        <footer>
            <p>MyQ Garage Door Control Interface</p>
        </footer>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
