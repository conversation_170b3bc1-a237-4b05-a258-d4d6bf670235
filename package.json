{"name": "myq", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "cli": "node cli.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@hjdhjd/myq": "^7.6.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-session": "^1.18.1", "myq-api": "^2.0.4"}, "devDependencies": {"nodemon": "^3.1.10"}}