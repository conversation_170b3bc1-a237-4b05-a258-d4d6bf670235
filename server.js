/**
 * MyQ API Server
 * This server provides REST API endpoints to control MyQ garage doors
 */

import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import session from 'express-session';
import myQClient from './client.js';

// Initialize dotenv
dotenv.config();

// ES modules fix for __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'myq-session-secret',
  resave: false,
  saveUninitialized: false,
  cookie: { 
    secure: process.env.NODE_ENV === 'production', 
    maxAge: 1000 * 60 * 60 * 24 // 24 hours
  }
}));

// Simple authentication middleware for API key method
function authenticateApiKey(req, res, next) {
  const apiKey = req.headers['x-api-key'];
  
  if (!apiKey) {
    return res.status(401).json({ error: 'API key required' });
  }
  
  next();
}

// Check if user is authenticated by session
function authenticateSession(req, res, next) {
  if (req.session && req.session.authenticated) {
    return next();
  }
  return res.status(401).json({ error: 'Authentication required' });
}

// Combined authentication middleware - allows API key or session
function authenticate(req, res, next) {
  // Check for API key first
  if (req.headers['x-api-key']) {
    return authenticateApiKey(req, res, next);
  }
  
  // Fall back to session authentication
  return authenticateSession(req, res, next);
}

// Login endpoint for direct authentication
app.post('/api/login', async (req, res) => {
  const { email, password } = req.body;
  
  if (!email || !password) {
    return res.status(400).json({ error: 'Email and password required' });
  }
  
  try {
    // Store credentials in memory for this session
    const sessionClient = {
      email,
      password,
      authenticated: false
    };
    
    // Configure the client with these credentials
    myQClient.email = email;
    myQClient.password = password;
    
    // Test login to verify credentials
    await myQClient.login();
    
    // If login successful, set session
    req.session.authenticated = true;
    req.session.email = email;
    
    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('Login error:', error.message);
    return res.status(401).json({ error: `Login failed: ${error.message}` });
  }
});

// Logout endpoint
app.post('/api/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      return res.status(500).json({ error: 'Failed to logout' });
    }
    res.status(200).json({ success: true });
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok' });
});

// API routes
app.get('/api/devices', authenticate, async (req, res) => {
  try {
    const devices = await myQClient.getDevices();
    res.json({ devices });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/device/:serialNumber', authenticate, async (req, res) => {
  try {
    const device = await myQClient.getDevice(req.params.serialNumber);
    res.json({ device });
  } catch (error) {
    res.status(error.message.includes('not found') ? 404 : 500).json({ error: error.message });
  }
});

app.get('/api/device/:serialNumber/state', authenticate, async (req, res) => {
  try {
    const state = await myQClient.getDoorState(req.params.serialNumber);
    res.json(state);
  } catch (error) {
    res.status(error.message.includes('not found') ? 404 : 500).json({ error: error.message });
  }
});

app.post('/api/device/:serialNumber/open', authenticate, async (req, res) => {
  try {
    const result = await myQClient.openDoor(req.params.serialNumber);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/device/:serialNumber/close', authenticate, async (req, res) => {
  try {
    const result = await myQClient.closeDoor(req.params.serialNumber);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`MyQ API Server running on port ${PORT}`);
});

// Handle authentication errors with MyQ service
myQClient.login().catch(err => {
  console.error('Failed to initialize MyQ client:', err.message);
  console.log('Please check your MyQ credentials in the .env file');
});
